# 🚗 麦克纳姆轮机器人移动和碰撞测试指南

## 概述

本指南将教您如何测试机器人小车的移动和碰撞功能，包括：
- 麦克纳姆轮全向移动（前后、左右、旋转、斜向移动）
- 物理碰撞检测
- 键盘控制操作

## 测试环境说明

### 世界文件特性
- **movement_test.world**: 专门设计的移动测试环境
  - 16m x 16m 封闭测试区域
  - 边界墙壁用于碰撞测试
  - 多种障碍物：方形、圆形障碍物
  - 狭窄通道测试区域
  - 优化的物理参数

### 机器人配置
- **麦克纳姆轮配置**: 4个独立控制的麦克纳姆轮
- **轮子参数**: 半径5cm，轮距35cm，轴距38cm
- **物理属性**: 摩擦系数1.0，优化的惯性参数

## 🚀 快速启动测试

### 方法1：完整系统测试（推荐）

```bash
# 1. 启动完整仿真环境
cd /home/<USER>/ros2_ws
source install/setup.bash
ros2 launch pkg_gazebo simple_movement_test.launch.py

# 2. 等待3-5秒让系统完全启动，然后在新终端启动键盘控制
source install/setup.bash
python3 src/pkg_gazebo/src/keyboard_teleop.py
```

### 方法2：Headless模式测试

```bash
# 启动无GUI模式（适合性能测试）
ros2 launch pkg_gazebo simple_movement_test.launch.py headless:=true

# 在新终端启动键盘控制
python3 src/pkg_gazebo/src/keyboard_teleop.py
```

## 🎮 键盘控制说明

### 基本移动控制
| 按键 | 功能 | 说明 |
|------|------|------|
| `w` | 前进 | 沿X轴正方向移动 |
| `s` | 后退 | 沿X轴负方向移动 |
| `a` | 左移 | 沿Y轴正方向平移 |
| `d` | 右移 | 沿Y轴负方向平移 |
| `q` | 左转 | 绕Z轴逆时针旋转 |
| `e` | 右转 | 绕Z轴顺时针旋转 |

### 斜向移动（麦克纳姆轮特色）
| 按键 | 功能 | 说明 |
|------|------|------|
| `r` | 右前斜移 | 同时前进和右移 |
| `t` | 左前斜移 | 同时前进和左移 |
| `f` | 右后斜移 | 同时后退和右移 |
| `g` | 左后斜移 | 同时后退和左移 |

### 速度控制
| 按键 | 功能 | 范围 |
|------|------|------|
| `u` | 增加线速度 | 0.1 - 3.0 m/s |
| `j` | 减少线速度 | 0.1 - 3.0 m/s |
| `i` | 增加角速度 | 0.1 - 3.0 rad/s |
| `k` | 减少角速度 | 0.1 - 3.0 rad/s |

### 其他控制
| 按键 | 功能 |
|------|------|
| `空格` | 紧急停止 |
| `h` | 显示帮助信息 |
| `Ctrl+C` | 退出控制程序 |

## 🧪 测试验证步骤

### 1. 基础移动测试

**目标**: 验证所有方向的移动功能

**步骤**:
1. 启动仿真环境
2. 依次测试每个方向键：`w`, `s`, `a`, `d`, `q`, `e`
3. 观察机器人是否按预期方向移动
4. 测试斜向移动：`r`, `t`, `f`, `g`

**预期结果**:
- ✅ 机器人能够前后移动
- ✅ 机器人能够左右平移（麦克纳姆轮特性）
- ✅ 机器人能够原地旋转
- ✅ 机器人能够斜向移动

### 2. 速度控制测试

**目标**: 验证速度调节功能

**步骤**:
1. 使用`u`/`j`调节线速度
2. 使用`i`/`k`调节角速度
3. 在不同速度下测试移动
4. 使用空格键测试紧急停止

**预期结果**:
- ✅ 速度可以平滑调节
- ✅ 紧急停止立即生效
- ✅ 控制台显示当前速度设置

### 3. 碰撞检测测试

**目标**: 验证物理碰撞反应

**测试区域**:
- 红色方形障碍物 (3, 2)
- 绿色方形障碍物 (-3, -2)
- 蓝色圆形障碍物 (0, 4)
- 黄色圆形障碍物 (-4, 3)
- 边界墙壁

**步骤**:
1. 驾驶机器人接近各种障碍物
2. 观察碰撞时的物理反应
3. 测试不同角度的碰撞
4. 尝试推动障碍物（应该是静态的）

**预期结果**:
- ✅ 机器人与障碍物发生真实的物理碰撞
- ✅ 机器人无法穿透障碍物
- ✅ 碰撞时机器人会停止或反弹
- ✅ 静态障碍物保持不动

### 4. 狭窄通道测试

**目标**: 测试精确控制能力

**测试位置**: 右下角狭窄通道 (x=5, y=-5.5附近)

**步骤**:
1. 驾驶机器人到狭窄通道入口
2. 尝试通过通道
3. 测试在通道中的转向能力
4. 验证机器人尺寸与通道的匹配

**预期结果**:
- ✅ 机器人能够进入通道
- ✅ 在通道中移动时有适当的间隙
- ✅ 如果通道太窄，机器人会被阻挡

### 5. 综合机动性测试

**目标**: 测试复杂移动场景

**测试场景**:
1. **绕障碍物移动**: 围绕中央障碍物做圆周运动
2. **S型路径**: 在障碍物间穿行
3. **原地调头**: 在狭小空间内180度转向
4. **精确定位**: 移动到指定位置并停止

**预期结果**:
- ✅ 机器人能够执行复杂的移动轨迹
- ✅ 麦克纳姆轮的全向移动优势明显
- ✅ 控制响应及时准确

## 🔧 故障排除

### 常见问题及解决方案

**问题1**: Gazebo启动失败
```bash
# 解决方案：检查依赖
apt list --installed | grep ros-jazzy-gz
# 如果缺少包，重新安装
sudo apt install ros-jazzy-ros-gz ros-jazzy-gz-ros2-control
```

**问题2**: 机器人不响应键盘控制
```bash
# 检查话题连接
ros2 topic list | grep cmd_vel
ros2 topic echo /cmd_vel

# 检查控制器状态
ros2 node list | grep controller
```

**问题3**: 机器人移动方向错误
- 检查麦克纳姆轮的安装方向
- 验证运动学参数设置
- 查看控制器日志输出

**问题4**: 碰撞检测不工作
- 确认世界文件正确加载
- 检查机器人碰撞几何体设置
- 验证物理引擎参数

### 调试命令

```bash
# 查看所有ROS话题
ros2 topic list

# 监控机器人状态
ros2 topic echo /joint_states

# 查看TF树
ros2 run tf2_tools view_frames

# 检查节点状态
ros2 node list
ros2 node info /gazebo_mecanum_controller
```

## 📊 性能指标

### 预期性能表现

| 指标 | 数值 | 说明 |
|------|------|------|
| 最大线速度 | 3.0 m/s | 可调节 |
| 最大角速度 | 3.0 rad/s | 可调节 |
| 控制延迟 | < 100ms | 键盘到运动响应 |
| 仿真频率 | 250 Hz | 物理更新频率 |
| 碰撞检测精度 | 1cm | 接触检测精度 |

### 测试通过标准

- [ ] 所有8个方向移动正常
- [ ] 速度控制响应准确
- [ ] 碰撞检测工作正常
- [ ] 紧急停止立即生效
- [ ] 复杂机动动作流畅
- [ ] 系统运行稳定无崩溃

## 🎯 下一步扩展

完成基础测试后，可以考虑：

1. **自动导航测试**: 集成Nav2进行自主导航
2. **传感器集成**: 添加激光雷达、摄像头等传感器
3. **路径规划**: 实现自动路径规划和跟踪
4. **多机器人仿真**: 在同一环境中运行多个机器人
5. **性能优化**: 调整物理参数提高仿真真实度

---

**注意**: 如果在测试过程中遇到任何问题，请查看终端输出的错误信息，并参考故障排除部分。
