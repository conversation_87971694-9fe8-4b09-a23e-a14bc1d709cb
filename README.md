# ROS2 机器人小车仿真项目

基于ROS2 Jazzy的全向移动机器人小车仿真系统，具备云台射击功能。

## 项目概述

这是一个完整的ROS2机器人仿真项目，包含：
- 四轮麦克纳姆轮全向移动底盘
- 两轴云台系统（yaw + pitch）
- 射击装置
- 完整的物理仿真支持
- ros2_control集成

## 已完成功能（阶段0：基线准备）

### ✅ URDF升级为xacro格式
- 参数化机器人模型
- 统一材料定义
- 轮子宏定义，便于维护
- 物理属性参数化

### ✅ 完善物理属性
- 优化惯性矩阵计算
- 改进碰撞几何（轮子使用圆柱体，底盘使用盒子）
- 添加摩擦系数和阻尼参数
- 关节动力学属性配置

### ✅ ros2_control支持
- 完整的ros2_control配置
- 轮子速度控制接口
- 云台位置/速度控制接口
- 射击装置速度控制接口
- Gazebo插件集成

## 文件结构

```
pkg_gazebo/
├── urdf/
│   ├── urbubing.urdf.xacro      # 参数化机器人模型
│   └── urbubing.urdf            # 原始URDF（保留）
├── launch/
│   ├── display.launch.py        # RViz显示
│   └── gazebo.launch.py         # Gazebo仿真
├── config/
│   ├── controllers.yaml         # ros2_control配置
│   └── joint_names_urbubing.yaml
├── worlds/
│   └── empty.world              # Gazebo世界文件
├── meshes/                      # STL网格文件
├── scripts/
│   └── test_robot.py            # 测试脚本
└── README.md
```

## 使用方法

### 1. 构建项目
```bash
cd /home/<USER>/ros2_ws
colcon build --packages-select pkg_gazebo
source install/setup.bash
```

### 2. 启动RViz可视化
```bash
ros2 launch pkg_gazebo display.launch.py
```

### 3. 启动Gazebo仿真
```bash
ros2 launch pkg_gazebo gazebo.launch.py
```

### 4. 测试关节状态
```bash
python3 src/pkg_gazebo/scripts/test_robot.py
```

## 技术特性

### 机器人配置
- **底盘**: 4个麦克纳姆轮，支持全向移动
- **云台**: yaw轴（连续旋转）+ pitch轴（-45°到30°）
- **射击**: 连续旋转射击装置
- **总质量**: ~11kg

### 控制接口
- **轮子控制**: 速度控制（rad/s）
- **云台控制**: 位置控制（rad）
- **射击控制**: 速度控制（rad/s）

### 物理参数
- **轮子摩擦系数**: 1.0
- **关节阻尼**: 0.1
- **关节摩擦**: 0.1
- **更新频率**: 100Hz

## 下一步开发计划

1. **Gazebo仿真环境搭建** - 创建完整的仿真世界
2. **运动控制系统开发** - 实现麦克纳姆轮运动学
3. **射击系统仿真** - 弹丸物理和射击逻辑
4. **传感器集成** - 激光雷达、IMU、摄像头
5. **自主导航系统** - Nav2集成
6. **人机交互界面** - 控制面板和监控

## 依赖包

- robot_state_publisher
- joint_state_publisher  
- xacro
- gazebo_ros
- ros2_control
- ros2_controllers
- gazebo_ros2_control
- controller_manager
- rviz2

## 注意事项

1. 确保已安装所有依赖包
2. 使用ROS2 Jazzy版本
3. Gazebo Classic或Ignition Gazebo都支持
4. 建议在Ubuntu 22.04上运行
