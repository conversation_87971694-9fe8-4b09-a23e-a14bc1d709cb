import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    
    # 控制器配置文件路径
    controllers_file = os.path.join(pkg_gazebo, 'config', 'controllers.yaml')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 启动Gazebo
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('gazebo_ros'), 'launch', 'gazebo.launch.py')
        ]),
        launch_arguments={
            'world': os.path.join(pkg_gazebo, 'worlds', 'empty.world'),
            'verbose': 'true'
        }.items()
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 在Gazebo中生成机器人
    spawn_entity = launch_ros.actions.Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=['-topic', 'robot_description', '-entity', 'urbubing'],
        output='screen'
    )
    
    # 控制器管理器
    controller_manager = launch_ros.actions.Node(
        package='controller_manager',
        executable='ros2_control_node',
        parameters=[{'robot_description': robot_description}, controllers_file],
        output='screen'
    )
    
    # 启动控制器
    joint_state_broadcaster_spawner = launch_ros.actions.Node(
        package='controller_manager',
        executable='spawner',
        arguments=['joint_state_broadcaster'],
        output='screen'
    )
    
    wheel_controller_spawner = launch_ros.actions.Node(
        package='controller_manager',
        executable='spawner',
        arguments=['wheel_controller'],
        output='screen'
    )
    
    gimbal_controller_spawner = launch_ros.actions.Node(
        package='controller_manager',
        executable='spawner',
        arguments=['gimbal_controller'],
        output='screen'
    )
    
    shooter_controller_spawner = launch_ros.actions.Node(
        package='controller_manager',
        executable='spawner',
        arguments=['shooter_controller'],
        output='screen'
    )
    
    return launch.LaunchDescription([
        gazebo,
        robot_state_publisher,
        spawn_entity,
        controller_manager,
        joint_state_broadcaster_spawner,
        wheel_controller_spawner,
        gimbal_controller_spawner,
        shooter_controller_spawner
    ])
