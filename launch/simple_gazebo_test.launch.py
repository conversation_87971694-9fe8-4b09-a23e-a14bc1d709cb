import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 启动Gazebo
    gazebo = launch_ros.actions.Node(
        package='ros_gz_sim',
        executable='gz_sim',
        arguments=['-v', '4', '-s'],  # -s for server mode (headless)
        output='screen'
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 在Gazebo中生成机器人
    spawn_entity = launch_ros.actions.Node(
        package='ros_gz_sim',
        executable='create',
        arguments=[
            '-topic', 'robot_description',
            '-name', 'urbubing',
            '-x', '0.0',
            '-y', '0.0', 
            '-z', '0.2'
        ],
        output='screen'
    )
    
    return launch.LaunchDescription([
        gazebo,
        robot_state_publisher,
        spawn_entity
    ])
