import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import DeclareLaunchArgument, TimerAction
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # Launch参数
    headless_arg = DeclareLaunchArgument(
        'headless',
        default_value='false',
        description='Run Gazebo in headless mode (no GUI)'
    )
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    world_file = os.path.join(pkg_gazebo, 'worlds', 'movement_test.world')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 启动Gazebo
    gazebo_args = [world_file, ' -v 4']
    headless = LaunchConfiguration('headless')
    
    gazebo = launch_ros.actions.Node(
        package='ros_gz_sim',
        executable='gzserver',
        arguments=[world_file, '-v', '4'],
        output='screen',
        condition=launch.conditions.UnlessCondition(headless)
    )

    gazebo_headless = launch_ros.actions.Node(
        package='ros_gz_sim',
        executable='gzserver',
        arguments=[world_file, '-v', '4', '-s'],
        output='screen',
        condition=launch.conditions.IfCondition(headless)
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 关节状态发布器
    joint_state_publisher = launch_ros.actions.Node(
        package='joint_state_publisher',
        executable='joint_state_publisher',
        name='joint_state_publisher',
        output='screen'
    )
    
    # 延迟生成机器人
    spawn_entity = TimerAction(
        period=3.0,
        actions=[
            launch_ros.actions.Node(
                package='ros_gz_sim',
                executable='spawn_entity',
                arguments=[
                    '-topic', 'robot_description',
                    '-name', 'urbubing',
                    '-x', '0.0',
                    '-y', '0.0',
                    '-z', '0.2'
                ],
                output='screen'
            )
        ]
    )
    
    # 麦克纳姆轮控制器（简化版，直接控制关节）
    mecanum_controller = TimerAction(
        period=5.0,
        actions=[
            launch_ros.actions.Node(
                package='pkg_gazebo',
                executable='gazebo_mecanum_controller.py',
                name='gazebo_mecanum_controller',
                output='screen'
            )
        ]
    )
    
    # ROS-Gazebo桥接
    gz_ros_bridge = launch_ros.actions.Node(
        package='ros_gz_bridge',
        executable='parameter_bridge',
        arguments=[
            '/clock@rosgraph_msgs/msg/Clock[gz.msgs.Clock',
            '/tf@tf2_msgs/msg/TFMessage[gz.msgs.Pose_V',
            '/tf_static@tf2_msgs/msg/TFMessage[gz.msgs.Pose_V',
            '/joint_states@sensor_msgs/msg/JointState[gz.msgs.Model'
        ],
        output='screen'
    )
    
    return launch.LaunchDescription([
        headless_arg,
        gazebo,
        gazebo_headless,
        robot_state_publisher,
        joint_state_publisher,
        spawn_entity,
        mecanum_controller,
        gz_ros_bridge
    ])
