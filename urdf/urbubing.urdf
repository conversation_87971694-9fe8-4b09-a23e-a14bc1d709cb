<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="urbubing">
  <link
    name="base_link">
    <inertial>
      <origin
        xyz="0.0432971520727676 0.0105027413251315 -0.0234599741467773"
        rpy="0 0 0" />
      <mass
        value="8.86791370010152" />
      <inertia
        ixx="0.0142338465075317"
        ixy="0.000558944709505001"
        ixz="-6.45174465414214E-05"
        iyy="0.020235487366205"
        iyz="2.48760063799849E-06"
        izz="0.0310177616926185" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="wheel_lf_Link">
    <inertial>
      <origin
        xyz="0.000265345929880895 -0.000118708607752468 -0.0226397732894146"
        rpy="0 0 0" />
      <mass
        value="0.254885792947332" />
      <inertia
        ixx="0.000108939063406329"
        ixy="-1.15453156708273E-06"
        ixz="-8.45799335200679E-08"
        iyy="0.00010634846361049"
        iyz="-5.94653917928581E-08"
        izz="0.000191411121109834" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_lf_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_lf_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="wheel_lf_Joint"
    type="continuous">
    <origin
      xyz="-0.19103 -0.17579 -0.073501"
      rpy="1.5708 0 -3.1416" />
    <parent
      link="base_link" />
    <child
      link="wheel_lf_Link" />
    <axis
      xyz="0 0 -1" />
    <limit
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="wheel_rf_Link">
    <inertial>
      <origin
        xyz="0.000290444031789294 4.72961132246541E-05 -0.0226397715237478"
        rpy="0 0 0" />
      <mass
        value="0.254885528480109" />
      <inertia
        ixx="0.000109611099518635"
        ixy="7.22345430664628E-07"
        ixz="-1.01734664568465E-07"
        iyy="0.000105674085148553"
        iyz="-1.84111861837377E-08"
        izz="0.000191408781189104" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_rf_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_rf_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="wheel_rf_Joint"
    type="continuous">
    <origin
      xyz="-0.19103 0.17321 -0.073501"
      rpy="-1.5708 0 3.1416" />
    <parent
      link="base_link" />
    <child
      link="wheel_rf_Link" />
    <axis
      xyz="0 0 -1" />
    <limit
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="wheel_rb_Link">
    <inertial>
      <origin
        xyz="-0.000265345907702663 -0.000118708674948537 0.0253295851235946"
        rpy="0 0 0" />
      <mass
        value="0.254885792415455" />
      <inertia
        ixx="0.000108939063843945"
        ixy="1.15453062837799E-06"
        ixz="-8.45794688965599E-08"
        iyy="0.000106348461840995"
        iyz="5.94656489436515E-08"
        izz="0.000191411119846991" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_rb_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_rb_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="wheel_rb_Joint"
    type="continuous">
    <origin
      xyz="0.18833 0.17052 -0.073501"
      rpy="1.5708 0 -3.1416" />
    <parent
      link="base_link" />
    <child
      link="wheel_rb_Link" />
    <axis
      xyz="0 0 -1" />
    <limit
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="wheel_lb_Link">
    <inertial>
      <origin
        xyz="-0.000293733140556973 1.77530441537038E-05 -0.022639772047863"
        rpy="0 0 0" />
      <mass
        value="0.254885519917945" />
      <inertia
        ixx="0.000109730634608958"
        ixy="-1.92423965660535E-07"
        ixz="1.03294821169717E-07"
        iyy="0.000105554640970902"
        iyz="-4.42030493604287E-09"
        izz="0.000191408866690139" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_lb_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/wheel_lb_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="wheel_lb_Joint"
    type="continuous">
    <origin
      xyz="0.18788 -0.17579 -0.054692"
      rpy="1.5708 0 3.1416" />
    <parent
      link="base_link" />
    <child
      link="wheel_lb_Link" />
    <axis
      xyz="0 0 -1" />
    <limit
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="yaw_Link">
    <inertial>
      <origin
        xyz="-0.00105608378572911 0.00375450379687958 -0.0921528912156362"
        rpy="0 0 0" />
      <mass
        value="1.34574810542162" />
      <inertia
        ixx="0.00119254241296655"
        ixy="-4.90978683056211E-07"
        ixz="2.50918559511305E-06"
        iyy="0.00127302578166741"
        iyz="-9.62484462634763E-07"
        izz="0.00153599058748134" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/yaw_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/yaw_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="yaw_Joint"
    type="continuous">
    <origin
      xyz="-0.0014015 -0.0018662 -0.050192"
      rpy="3.1416 0 0" />
    <parent
      link="base_link" />
    <child
      link="yaw_Link" />
    <axis
      xyz="0 0 -1" />
    <limit
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="pitch_Link">
    <inertial>
      <origin
        xyz="-0.000660825931109735 0.000125747113190827 0.0368612940049887"
        rpy="0 0 0" />
      <mass
        value="0.232186075150672" />
      <inertia
        ixx="7.30073800729081E-05"
        ixy="-5.14621394946364E-07"
        ixz="-2.31916909158694E-07"
        iyy="7.55479055414904E-05"
        iyz="4.26271355145143E-07"
        izz="0.000110005445018448" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="pitch_Joint"
    type="revolute">
    <origin
      xyz="0.00047466 -0.059676 -0.1331"
      rpy="-1.5708 0 0.0071369" />
    <parent
      link="yaw_Link" />
    <child
      link="pitch_Link" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-0.7854"
      upper="0.5236"
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="pitch_aid_Link1">
    <inertial>
      <origin
        xyz="4.33362215481875E-05 0.0376810264702473 -0.00607205227133864"
        rpy="0 0 0" />
      <mass
        value="0.0284426822264771" />
      <inertia
        ixx="2.61361437949981E-05"
        ixy="-2.50931180327187E-19"
        ixz="-1.15374767907533E-09"
        iyy="1.01177265417115E-06"
        iyz="-2.25697231145407E-21"
        izz="2.6297792852574E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_aid_Link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.776470588235294 0.756862745098039 0.737254901960784 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_aid_Link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="pitch_aid_Joint1"
    type="revolute">
    <origin
      xyz="-0.04 0 0.0015"
      rpy="0 0.0071369 0" />
    <parent
      link="pitch_Link" />
    <child
      link="pitch_aid_Link1" />
    <axis
      xyz="0.0071368 0 -0.99997" />
    <limit
      lower="-1.5708"
      upper="1.5078"
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="pitch_aid_Link2">
    <inertial>
      <origin
        xyz="0.0231873221663826 -0.00269569306105638 0.0006382093745253"
        rpy="0 0 0" />
      <mass
        value="0.0525879808870972" />
      <inertia
        ixx="5.26795907742542E-06"
        ixy="5.54023325186128E-07"
        ixz="-6.16868287450195E-19"
        iyy="1.18232350981482E-05"
        iyz="2.98943073376664E-20"
        izz="1.58800109121266E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_aid_Link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.776470588235294 0.756862745098039 0.737254901960784 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_aid_Link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="pitch_aid_Joint2"
    type="revolute">
    <origin
      xyz="0.00010777 0.121 -0.0151"
      rpy="0 -0.0071369 0" />
    <parent
      link="pitch_aid_Link1" />
    <child
      link="pitch_aid_Link2" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-0.5236"
      upper="0.7854"
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="pitch_aid_Link3">
    <inertial>
      <origin
        xyz="0.00998594838506346 0.0101003265745883 0.0699748929192421"
        rpy="0 0 0" />
      <mass
        value="1.57956286586645" />
      <inertia
        ixx="0.000820610095467701"
        ixy="1.96050511500634E-05"
        ixz="-1.05211974788944E-06"
        iyy="0.00162834327540048"
        iyz="4.04798144959797E-07"
        izz="0.00109666681474034" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_aid_Link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.776470588235294 0.756862745098039 0.737254901960784 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/pitch_aid_Link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="pitch_aid_Link3"
    type="revolute">
    <origin
      xyz="0.04 0 0"
      rpy="0 0 0" />
    <parent
      link="pitch_aid_Link2" />
    <child
      link="pitch_aid_Link3" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-0.5236"
      upper="0.7854"
      effort="100"
      velocity="100" />
  </joint>
  <link
    name="dials_Link">
    <inertial>
      <origin
        xyz="-0.000401308990043536 -0.000614658686248881 0.0206666698462531"
        rpy="0 0 0" />
      <mass
        value="0.191607659470622" />
      <inertia
        ixx="0.000133402350419207"
        ixy="-2.17730459620246E-06"
        ixz="3.83907510541521E-10"
        iyy="0.000131171244191974"
        iyz="6.80021326733225E-10"
        izz="0.000254090705830729" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/dials_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://pkg_gazebo/meshes/dials_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="dials_Joint"
    type="continuous">
    <origin
      xyz="-0.064978 -0.0010679 -0.2856"
      rpy="0 0 0" />
    <parent
      link="yaw_Link" />
    <child
      link="dials_Link" />
    <axis
      xyz="-0.10617 0.0076706 -0.99432" />
    <limit
      effort="100"
      velocity="100" />
  </joint>
</robot>